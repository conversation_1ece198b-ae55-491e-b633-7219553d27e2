// ignore_for_file: unnecessary_string_interpolations

import 'package:bibl/controllers/auth_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/edit_profile.dart';
import 'package:bibl/notifications_screen.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/views/podrska.dart';
import 'package:bibl/widgets/rate_app_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

import 'widgets/customappbar.dart';
import 'widgets/profile_photo.dart';

class Settings extends StatefulWidget {
  const Settings({super.key});

  @override
  State<Settings> createState() => _SettingsState();
}

class _SettingsState extends State<Settings> {
  final AuthController authController = Get.find();
  final ProfileController profileController = Get.find();

  Future<void> _launchUrl(url) async {
    final Uri urll = Uri.parse(url);
    await launchUrl(urll);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: CustomAppBar(
          height: 150.0, // Set height to 150 for other screens
          widget: null,
          isBackButton: false,
          title: 'Podešavanja',
        ),
        body: ScrollConfiguration(
          behavior: const ScrollBehavior(),
          child: SingleChildScrollView(
            child: Column(children: [
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    userProfileWidgetRow(),
                    editProfileWidgetRow(
                      ontap: () {
                        // WidgetsBinding.instance.addPostFrameCallback((_) {
                        //   Get.dialog(
                        //     const RateAppDialog(),
                        //   );
                        // });

                        _launchUrl('https://umnilab.com/politika-privatnosti');
                      },
                      iconPath: 'assets/svgs/politica_icon.svg',
                      text: 'Politika Privatnosti',
                    ),
                    editProfileWidgetRow(
                      ontap: () {
                        _launchUrl('https://umnilab.com/uslovi-koriscenja');
                      },
                      iconPath: 'assets/svgs/uslovi_kori_icon.svg',
                      text: 'Uslovi Koriśćenja',
                    ),
                    editProfileWidgetRow(
                      ontap: () {
                        _launchUrl('https://umnilab.com/uslovi-pretplate');
                      },
                      iconPath: 'assets/svgs/uslovi_pret_icon.svg',
                      text: 'Uslovi pretplate',
                    ),
                    editProfileWidgetRow(
                      ontap: () {
                        Get.to(() => const Podrska());
                      },
                      iconPath: 'assets/svgs/podrska_icon.svg',
                      text: 'Podrška',
                    ),
                    editProfileWidgetRow(
                      ontap: () {},
                      trailing: soundSwitchWidget(),
                      iconPath: 'assets/svgs/zvuk_icon.svg',
                      text: 'Zvuk',
                    ),
                    editProfileWidgetRow(
                      ontap: () {
                        Get.to(() => const NotificationScreen());
                      },
                      trailing: notificationSwitchWidget(),
                      iconPath: 'assets/svgs/notification_icon.svg',
                      text: 'Notifikacije',
                    ),
                    Obx(
                      () => editProfileWidgetRow(
                        trailing: authController.isSigningOut.value
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  color: mainColor,
                                ),
                              )
                            : null,
                        ontap: () async {
                          await profileController.logTimeSpent();
                          authController.signOut();
                        },
                        icon: const Icon(Icons.logout),
                        // iconPath: 'assets/svgs/pozovite_icon.svg',
                        // text: 'Pozovite prijatelja (referral)',
                        text: 'Logout',
                      ),
                    ),
                  ],
                ),
              ),
            ]),
          ),
        ),
      ),
    );
  }

  Theme notificationSwitchWidget() {
    return Theme(
      data: ThemeData(
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: Obx(
        () => SizedBox(
          width: 50,
          height: 20,
          child: Switch(
            value: profileController.isNotificationOn.value,
            onChanged: (value) {
              profileController.setNotificationStatus(value);
            },
            activeColor: mainColor,
            activeTrackColor: mainColor.withValues(alpha: 0.5),
          ),
        ),
      ),
    );
  }

  Theme soundSwitchWidget() {
    return Theme(
      data: ThemeData(
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: Obx(
        () => SizedBox(
          width: 50,
          height: 20,
          child: Switch(
            value: profileController.isSoundOn.value,
            onChanged: (value) {
              profileController.setSoundStatus(value);
            },
            activeColor: mainColor,
            activeTrackColor: mainColor.withValues(alpha: 0.5),
          ),
        ),
      ),
    );
  }

  Column editProfileWidgetRow({
    required String text,
    String? iconPath,
    Icon? icon,
    Widget? trailing,
    Function()? ontap,
  }) {
    return Column(
      children: [
        GestureDetector(
          onTap: ontap,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30), // Rounded corners
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.08), // Shadow color
                  spreadRadius: 0, // Spread of the shadow
                  blurRadius: 16, // Blur effect
                  offset: const Offset(0, 2), // Shadow position (x, y)
                ),
              ],
            ),
            padding: const EdgeInsets.all(8.0), // Button padding
            child: Row(
              children: [
                const SizedBox(width: 10), // Spacer
                icon ??
                    SvgPicture.asset(iconPath!), // Display the icon or fallback
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        SizedBox(width: Get.width * 0.03), // Spacer
                        Expanded(
                          child: Txt(
                            txt: text,
                            maxLines: 2,
                            fontSize: 14,
                          ), // Text widget
                        ),
                        trailing ??
                            const SizedBox.shrink(), // Optional trailing widget
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(
          height: 20,
        )
      ],
    );
  }

  Column userProfileWidgetRow() {
    final ProfileController profileController = Get.find();
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.all(
              Radius.circular(10),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08), // Shadow color
                spreadRadius: 0, // Spread of the shadow
                blurRadius: 16, // Blur effect
                offset: const Offset(0, 2), // Shadow position (x, y)
              ),
            ],
          ),
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              const ProfilePhotoWidget(
                isOnSettings: true,
              ),
              const SizedBox(
                width: 16,
              ),
              Expanded(
                  child: Obx(
                () => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Txt(
                      txt:
                          '${profileController.userr.value.name ?? ''} ${profileController.userr.value.surname ?? ''}',
                      fontSize: 16,
                    ),
                    Txt(
                      txt: '${profileController.userr.value.uniqueName ?? ''}',
                      fontSize: 12,
                      fontColor: Colors.grey,
                    ),
                  ],
                ),
              )),
              IconButton(
                onPressed: () {
                  Get.to(() => const EditProfile());
                },
                icon: CircleAvatar(
                  radius: 12,
                  backgroundColor: mainColor.withValues(alpha: 0.2),
                  child: const Icon(
                    Icons.edit,
                    size: 14,
                    color: mainColor,
                  ),
                ),
              )
            ],
          ),
        ),
        const SizedBox(
          height: 20,
        )
      ],
    );
  }
}
