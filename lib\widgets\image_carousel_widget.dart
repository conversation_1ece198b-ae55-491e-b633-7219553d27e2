import 'package:bibl/controllers/lesson_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:get/get.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import '../models/lesson_model.dart';
import '../models/quiz_model.dart';

class MyImageCarousel extends StatefulWidget {
  final LessonModel? lesson;
  final QuizModel? quiz;
  final ShuffleQuizModel? shuffleQuiz;

  const MyImageCarousel({super.key, this.lesson, this.quiz, this.shuffleQuiz});

  @override
  State<MyImageCarousel> createState() => _MyImageCarouselState();
}

class _MyImageCarouselState extends State<MyImageCarousel> {
  final CarouselSliderController _carouselController =
      CarouselSliderController();
  final ProfileController profileController = Get.find();
  final LessonController lessonController = Get.find();
  int _current = 0;
  List<Widget> _carouselItems = [];

  @override
  void initState() {
    super.initState();
    _initializeCarousel();
  }

  void _initializeCarousel() {
    if (widget.lesson != null) {
      final pages = widget.lesson!.pages ?? [];
      debugPrint(
          'Lesson ${widget.lesson!.lessonName} has ${pages.length} pages');

      final validPages = pages
          .where((page) =>
              page.pagePhotoLink != null && page.pagePhotoLink!.isNotEmpty)
          .toList();
      debugPrint('Valid pages with images: ${validPages.length}');

      _carouselItems = validPages.map((page) {
        debugPrint('Page image URL: ${page.pagePhotoLink}');
        return _buildImageWidget(page.pagePhotoLink!, widget.lesson!.lessonId!);
      }).toList();

      // If no valid images, show placeholder
      if (_carouselItems.isEmpty) {
        debugPrint('No valid images found for lesson, showing placeholder');
        _carouselItems = [_buildPlaceholderWidget()];
      }
    } else if (widget.shuffleQuiz != null) {
      final questions = widget.shuffleQuiz!.questionsList ?? [];
      debugPrint(
          'Shuffle quiz ${widget.shuffleQuiz!.quizName} has ${questions.length} questions');

      final validQuestions = questions
          .where((question) =>
              question.qsImage != null && question.qsImage!.isNotEmpty)
          .toList();
      debugPrint('Valid questions with images: ${validQuestions.length}');

      _carouselItems = validQuestions.map((question) {
        debugPrint('Question image URL: ${question.qsImage}');
        return _buildImageWidget(
            question.qsImage!, widget.shuffleQuiz!.quizId!);
      }).toList();

      // If no valid images, show placeholder
      if (_carouselItems.isEmpty) {
        debugPrint(
            'No valid images found for shuffle quiz, showing placeholder');
        _carouselItems = [_buildPlaceholderWidget()];
      }
    } else if (widget.quiz != null) {
      debugPrint(
          'Quiz ${widget.quiz!.quizName} image URL: ${widget.quiz!.quizImageLink}');

      if (widget.quiz!.quizImageLink != null &&
          widget.quiz!.quizImageLink!.isNotEmpty) {
        _carouselItems = [
          _buildImageWidget(widget.quiz!.quizImageLink!, widget.quiz!.quizId!)
        ];
      } else {
        debugPrint('No valid image found for quiz, showing placeholder');
        _carouselItems = [_buildPlaceholderWidget()];
      }
    }

    // Trigger background prefetching
    if (widget.lesson != null) {
      final urls =
          widget.lesson!.pages?.map((p) => p.pagePhotoLink ?? '').toList() ??
              [];
      lessonController
          .prefetchImages(urls, widget.lesson!.lessonId!)
          .catchError((e) {
        debugPrint('Error preloading lesson images: $e');
      });
    } else if (widget.shuffleQuiz != null) {
      final urls = widget.shuffleQuiz!.questionsList
              ?.map((q) => q.qsImage ?? '')
              .toList() ??
          [];
      lessonController
          .prefetchImages(urls, widget.shuffleQuiz!.quizId!)
          .catchError((e) {
        debugPrint('Error preloading shuffle quiz images: $e');
      });
    } else if (widget.quiz != null) {
      lessonController.prefetchImages([widget.quiz!.quizImageLink ?? ''],
          widget.quiz!.quizId!).catchError((e) {
        debugPrint('Error preloading quiz image: $e');
      });
    }
  }

  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasAbsolutePath &&
          (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  Widget _buildImageWidget(String imageUrl, String prefix) {
    // Validate URL before creating widget
    if (imageUrl.isEmpty || !_isValidUrl(imageUrl)) {
      return _buildPlaceholderWidget();
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(14),
      child: CachedNetworkImage(
        imageUrl: imageUrl,
        cacheManager: lessonController.cacheManager,
        fit: BoxFit.cover,
        width: double.infinity,
        height: 360,
        placeholder: (context, url) => Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(14),
            color: mainColor.withValues(alpha: 0.1),
          ),
          child: const Center(
            child: SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: mainColor,
              ),
            ),
          ),
        ),
        errorWidget: (context, url, error) {
          debugPrint('Image load error for $url: $error');
          return _buildPlaceholderWidget();
        },
      ),
    );
  }

  Widget _buildPlaceholderWidget() {
    return Container(
      height: 360,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(14),
        color: mainColor.withValues(alpha: 0.1),
        border: Border.all(
          color: mainColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.image_outlined,
              size: 48,
              color: mainColor,
            ),
            SizedBox(height: 8),
            Text(
              'No image available',
              style: TextStyle(
                color: mainColor,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.lesson != null || widget.shuffleQuiz != null)
          _buildCarousel()
        else if (widget.quiz != null)
          _buildSingleQuizImage(),
      ],
    );
  }

  Widget _buildCarousel() {
    return Column(
      children: [
        CarouselSlider(
          carouselController: _carouselController,
          options: CarouselOptions(
            enlargeCenterPage: true,
            height: 360,
            viewportFraction: 1,
            onPageChanged: (index, reason) {
              setState(() => _current = index);
              debugPrint('Swiped to page $index');
            },
          ),
          items: _carouselItems,
        ),
        const SizedBox(height: 16),
        AnimatedSmoothIndicator(
          activeIndex: _current,
          count: _carouselItems.length,
          effect: const ScrollingDotsEffect(
            dotHeight: 8,
            dotWidth: 8,
            activeDotColor: mainColor,
          ),
          onDotClicked: (index) => _carouselController.animateToPage(index),
        ),
      ],
    );
  }

  Widget _buildSingleQuizImage() {
    return SizedBox(
      height: 360,
      width: double.infinity,
      child: _carouselItems.first,
    );
  }
}
