import 'dart:io';
import 'package:bibl/controllers/auth_controller.dart';
import 'package:bibl/models/category_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../res/style.dart';
import '../utils/icon_utils.dart';

class CategoryController extends GetxController {
  RxList<CategoryModel> allCategories = <CategoryModel>[].obs;

  String? getImageLink(String category) {
    for (var cat in allCategories) {
      if (cat.topicName == category) {
        return cat.topicPhotoLink;
      }
    }
    return null;
  }

  Future<File?> getSvgFile(String topicPhotoLink) async {
    // return null;
    return IconUtils.fetchAndCacheImage(
      IconUtils.validateSvgUrl(topicPhotoLink),
      '$topicPhotoLink.svg',
    );
  }

  Future<void> fetchAllCategories() async {
    try {
      QuerySnapshot snapshot = await firestore.collection('topics').get();
      List<CategoryModel> categories =
          snapshot.docs.map((doc) => CategoryModel.fromSnap(doc)).toList();

      allCategories.assignAll(categories);

      categories.map((category) async {
        final topicPhotoLink = category.topicPhotoLink ?? '';

        await IconUtils.fetchAndCacheImage(
          IconUtils.validateSvgUrl(topicPhotoLink),
          '$topicPhotoLink.svg',
        );
      });
    } catch (e) {
      //
    }
  }

  void addSelectedCategoriesToDatabase(List<String> selectedCategories) {
    AuthController authController = Get.find();

    if (authController.user != null) {
      var userDoc = firestore.collection('users').doc(authController.user!.uid);

      // First, clear the existing list
      userDoc.update({
        'listOfFavCategories': [],
      }).then((_) {
        // Then, add the new list of selected categories
        userDoc.update({
          'listOfFavCategories': FieldValue.arrayUnion(selectedCategories),
        });
      });
    }
  }

  void addSelectedLibraryCategoriesToDatabase(List<String> selectedCategories) {
    AuthController authController = Get.find();

    if (authController.user != null) {
      var userDoc = firestore.collection('users').doc(authController.user!.uid);

      // First, clear the existing list
      userDoc.update({
        'listOfLibraryCategories': [],
      }).then((_) {
        // Then, add the new list of selected categories
        userDoc.update({
          'listOfLibraryCategories': FieldValue.arrayUnion(selectedCategories),
        });
      });
    }
  }
}
