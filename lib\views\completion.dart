import 'dart:async';

import 'package:bibl/controllers/lesson_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/services/lessonquiz_completion_service.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../bnb.dart';
import '../services/ad_mob_service.dart';
import '../services/reward_service.dart';
import '../widgets/customappbar.dart';

class Completion extends StatefulWidget {
  final String id;
  final bool isFromLesson;
  final int earnedNeurons;
  const Completion(
      {super.key,
      required this.id,
      required this.earnedNeurons,
      required this.isFromLesson});

  @override
  State<Completion> createState() => _CompletionState();
}

class _CompletionState extends State<Completion> {
  ProfileController profileController = Get.find();
  LessonController lessonController = Get.find();
  int countdown = 3; // Countdown starts from 3
  bool isCountingDown = false; // State to control button appearance
  final LessonQuizCompletionService completionService =
      LessonQuizCompletionService();
  late InterstitialAdManager lessonAdManager;
  bool shouldShowEarnedNeurons =
      false; // Track if user should see earned neurons

  @override
  void initState() {
    super.initState(); // Initialize lessonAdManager in initState
    lessonAdManager = InterstitialAdManager(
      context: widget.isFromLesson ? 'lessons' : 'quizzes',
    );
    if (!profileController.userr.value.isPremiumUser!) {
      lessonAdManager.loadAd();
    }

    // Check if this lesson/quiz was completed within the last 30 days
    bool isCompletedWithinThirtyDays =
        completionService.isCompletedWithinThirtyDays(widget.id,
            profileController.userr.value.completedLessonQuizesInThirtyDays);

    // User should only get neurons and see the earned neurons if NOT completed within 30 days
    shouldShowEarnedNeurons = !isCompletedWithinThirtyDays;

    if (!isCompletedWithinThirtyDays) {
      // Add completion record and give rewards only if not completed within 30 days
      completionService.addCompletedLessonQuizAndUpdateFirestore(
          profileController.userr.value.uid!, widget.id);

      // Check if the UID is available
      if (profileController.userr.value.uid != null) {
        // Initialize the RewardService with the user's UID
        RewardService rewardService =
            RewardService(profileController.userr.value.uid!);
        rewardService.addNeurons(widget.earnedNeurons);

        if (profileController.userr.value.completedArticlesCount! == 5) {
          rewardService.noviceReader();
        }
        if (profileController.userr.value.completedArticlesCount! == 10) {
          rewardService.intermediateReader();
        }
        if (profileController.userr.value.completedArticlesCount! == 20) {
          rewardService.advancedReader();
        }
        if (profileController.userr.value.completedArticlesCount! == 50) {
          rewardService.articleEnthusiast();
        }
      }
    }
  }

  void _startCountdown() {
    lessonController.mergeAndShuffleItems(
        shouldClear: false, isShuffle: true, from: 'completion');
    if (!profileController.userr.value.isPremiumUser!) {
      setState(() {
        isCountingDown = true;
      });
      Timer.periodic(const Duration(seconds: 1), (timer) {
        if (countdown > 1) {
          setState(() {
            countdown--;
          });
        } else {
          timer.cancel();
          // lessonAdManager.showAd();
          Get.offUntil(
            GetPageRoute(page: () => const BNB()),
            (route) => route.isFirst,
          );
        }
      });
    } else {
      Get.offUntil(
        GetPageRoute(page: () => const BNB()),
        (route) => route.isFirst,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: SafeArea(
        child: Scaffold(
          backgroundColor: Colors.white,
          appBar: CustomAppBar(
            widget: null,
            isBackButton: false,
            title: 'Bravo',
          ),
          body: Stack(
            children: [
              ScrollConfiguration(
                behavior: const ScrollBehavior(),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      SizedBox(height: Get.height * 0.03),
                      SizedBox(
                        height: 492,
                        child: Stack(
                          children: [
                            Positioned.fill(
                                child: SvgPicture.asset(
                                    'assets/svgs/success.svg')),
                            // Only show earned neurons if user is eligible for points
                            if (shouldShowEarnedNeurons)
                              Align(
                                alignment: Alignment.bottomCenter,
                                child: Padding(
                                  padding: const EdgeInsets.only(bottom: 45),
                                  child: Container(
                                    width: 192,
                                    height: 30,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                            50), // Rounded corners
                                        gradient: weekDayTitleGradient),
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color: const Color(
                                            0xffF8EFFF), // Background color inside the gradient border
                                        borderRadius: BorderRadius.circular(
                                            50), // Match the border radius
                                      ),
                                      margin: const EdgeInsets.all(
                                          1.5), // Add spacing for the border width
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          const Txt(
                                              txt: 'Osvojeno', fontSize: 12),
                                          Txt(
                                              txt: ' ${widget.earnedNeurons} ',
                                              fontSize: 12),
                                          const Txt(
                                              txt: 'neurona', fontSize: 12),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                      SizedBox(height: Get.height * 0.1),
                    ],
                  ),
                ),
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: buttonContainer(
                    text: isCountingDown
                        ? 'Učitavanje reklame ($countdown)'
                        : 'Nastavi',
                    onTap: _startCountdown, // Start countdown on tap
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
