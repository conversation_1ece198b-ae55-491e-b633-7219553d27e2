import 'package:bibl/controllers/auth_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'widgets/customappbar.dart';
import 'widgets/profile_photo.dart';

class EditProfile extends StatefulWidget {
  const EditProfile({super.key});

  @override
  State<EditProfile> createState() => _EditProfileState();
}

class _EditProfileState extends State<EditProfile> {
  final AuthController authController = Get.find();
  final ProfileController profileController = Get.find();
  final nameController = TextEditingController();
  final surnameController = TextEditingController();
  final currentPasswordController = TextEditingController();
  final newPasswordController = TextEditingController();
  bool isPasswordObscure = true;
  @override
  void dispose() {
    super.dispose();
    nameController.dispose();
    surnameController.dispose();
    currentPasswordController.dispose();
    newPasswordController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: CustomAppBar(
          widget: null,
          title: 'Izmeni Profil',
        ),
        body: Stack(
          children: [
            ScrollConfiguration(
              behavior: const ScrollBehavior(),
              child: SingleChildScrollView(
                child: Column(children: [
                  Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const ProfilePhotoWidget(
                          isOnSettings: false,
                        ),
                        SizedBox(
                          height: Get.height * 0.01,
                        ),
                        const Txt(
                          txt: 'Ime',
                          fontSize: 14,
                        ),
                        const SizedBox(height: 8.0),
                        Obx(
                          () => textFieldContainer(
                            context,
                            controller: nameController,
                            hint: profileController.userr.value.name ?? 'Ime',
                            prefix: const Padding(
                              padding: EdgeInsets.all(12.0),
                              child: Icon(
                                Icons.person_outline,
                                size: 25,
                              ),
                            ),
                            padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                          ),
                        ),
                        SizedBox(
                          height: Get.height * 0.01,
                        ),
                        const Txt(
                          txt: 'Prezime',
                          fontSize: 14,
                        ),
                        const SizedBox(height: 8.0),
                        Obx(
                          () => textFieldContainer(
                            context,
                            controller: surnameController,
                            hint: profileController.userr.value.surname ??
                                'Prezime',
                            prefix: const Padding(
                              padding: EdgeInsets.all(12.0),
                              child: Icon(
                                Icons.person_outline,
                                size: 25,
                              ),
                            ),
                            padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                          ),
                        ),
                        SizedBox(
                          height: Get.height * 0.01,
                        ),
                        const Txt(
                          txt: 'Trenutna Lozinka',
                          fontSize: 14,
                        ),
                        const SizedBox(height: 8.0),
                        textFieldContainer(
                          context,
                          maxLines: 1,
                          isObscure: isPasswordObscure ? true : null,
                          trailing: isPasswordObscure
                              ? GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      isPasswordObscure = false;
                                    });
                                  },
                                  child: const Padding(
                                    padding:
                                        EdgeInsets.only(top: 10, bottom: 10),
                                    child: Icon(
                                      Icons.visibility_off_outlined,
                                      size: 20,
                                      color: greyishColor,
                                    ),
                                  ),
                                )
                              : GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      isPasswordObscure = true;
                                    });
                                  },
                                  child: const Padding(
                                    padding:
                                        EdgeInsets.only(top: 10, bottom: 10),
                                    child: Icon(
                                      Icons.visibility_outlined,
                                      size: 20,
                                      color: secondaryColor,
                                    ),
                                  ),
                                ),
                          controller: currentPasswordController,
                          hint: 'Trenutna Lozinka',
                          prefix: const Padding(
                            padding: EdgeInsets.all(12.0),
                            child: Icon(
                              Icons.lock_outline,
                              color: grey2Color,
                            ),
                          ),
                          padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                        ),
                        SizedBox(
                          height: Get.height * 0.01,
                        ),
                        const Txt(
                          txt: 'Potvrdi Novu Lozinku',
                          fontSize: 14,
                        ),
                        const SizedBox(height: 8.0),
                        textFieldContainer(
                          context,
                          maxLines: 1,
                          isObscure: isPasswordObscure ? true : null,
                          trailing: isPasswordObscure
                              ? GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      isPasswordObscure = false;
                                    });
                                  },
                                  child: const Padding(
                                    padding:
                                        EdgeInsets.only(top: 10, bottom: 10),
                                    child: Icon(
                                      Icons.visibility_off_outlined,
                                      size: 20,
                                      color: greyishColor,
                                    ),
                                  ),
                                )
                              : GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      isPasswordObscure = true;
                                    });
                                  },
                                  child: const Padding(
                                    padding:
                                        EdgeInsets.only(top: 10, bottom: 10),
                                    child: Icon(
                                      Icons.visibility_outlined,
                                      size: 20,
                                      color: secondaryColor,
                                    ),
                                  ),
                                ),
                          controller: newPasswordController,
                          hint: 'Nova Lozinka',
                          prefix: const Padding(
                            padding: EdgeInsets.all(12.0),
                            child: Icon(
                              Icons.lock_outline,
                              color: grey2Color,
                            ),
                          ),
                          padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                        ),
                        const SizedBox(
                          height: 150,
                        )
                      ],
                    ),
                  ),
                ]),
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Obx(
                  () => buttonContainer(
                    text: 'Sačuvaj',
                    child: profileController.isProfileUpdating.value
                        ? const Center(
                            child: CircularProgressIndicator(
                              color: Colors.white,
                            ),
                          )
                        : null,
                    onTap: () {
                      profileController.updateProfile(
                          name: nameController.text,
                          surname: surnameController.text,
                          currentPassword: currentPasswordController.text,
                          newPassword: newPasswordController.text);

                      currentPasswordController.clear();
                      newPasswordController.clear();
                    },
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
