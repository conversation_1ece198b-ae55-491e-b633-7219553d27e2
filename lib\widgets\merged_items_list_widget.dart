import 'dart:io';
import 'package:bibl/controllers/lesson_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/models/lesson_model.dart';
import 'package:bibl/models/quiz_model.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/box_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'interests_widget.dart';

class MergedItemsList extends StatefulWidget {
  final bool isForLibrary;
  final ScrollController scrollController;

  const MergedItemsList({
    Key? key,
    required this.isForLibrary,
    required this.scrollController,
  }) : super(key: key);

  @override
  State<MergedItemsList> createState() => _MergedItemsListState();
}

class _MergedItemsListState extends State<MergedItemsList> {
  final ProfileController profileController = Get.find();
  final LessonController lessonController = Get.find();
  final Set<int> _loadedAdIndices = {};
  final Map<int, BannerAd> _bannerAds = {};
  bool _showScrollToTopButton = false;
  bool _isLoadingMore = false; // Track if we're loading more items

  // Ad units map
  final Map<int, String> _adUnitMap = Platform.isAndroid
      ? {
          3: 'ca-app-pub-8639821055582439/1096152024',
          6: 'ca-app-pub-8639821055582439/9477204925',
          9: 'ca-app-pub-8639821055582439/4156563660',
          -1: 'ca-app-pub-8639821055582439/8056296139',
        }
      : {
          3: 'ca-app-pub-8639821055582439/3769550858',
          6: 'ca-app-pub-8639821055582439/4265224933',
          9: 'ca-app-pub-8639821055582439/1746888714',
          -1: 'ca-app-pub-8639821055582439/2021354915',
        };

  @override
  void initState() {
    super.initState();
    widget.scrollController.addListener(_onScroll);
    if (!(profileController.userr.value.isPremiumUser ?? false)) {
      // Preload initial ads
      _createBannerAd(3);
      _createBannerAd(6);
      _createBannerAd(9);
      // Preload next ads for smooth scrolling
      _createBannerAd(12);
      _createBannerAd(15);
    }
    // Preload next set of items
    _preloadNextItems();
  }

  @override
  void dispose() {
    widget.scrollController.removeListener(_onScroll);
    for (var ad in _bannerAds.values) {
      ad.dispose();
    }
    _bannerAds.clear();
    super.dispose();
  }

  // Debounced scroll listener
  void _onScroll() {
    double currentOffset = widget.scrollController.position.pixels;
    bool shouldShowButton = currentOffset > AppConstants.minScrollForButton;
    if (shouldShowButton != _showScrollToTopButton) {
      setState(() => _showScrollToTopButton = shouldShowButton);
    }

    if (!_isLoadingMore &&
        currentOffset >=
            widget.scrollController.position.maxScrollExtent -
                AppConstants.infiniteScrollOffset) {
      _loadMoreItems();
    }
  }

  // Scroll to top with animation
  void _scrollToTop() {
    setState(() => _showScrollToTopButton = false);
    widget.scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  // Preload next set of items and their images aggressively
  void _preloadNextItems() {
    final currentLength = widget.isForLibrary
        ? lessonController.libraryDisplayedItems.length
        : lessonController.homeDisplayedItems.length;

    // Preload more items ahead for smoother scrolling
    final nextChunk = (widget.isForLibrary
            ? lessonController.allItems
            : lessonController.mergedItems)
        .skip(currentLength)
        .take(lessonController.homeItemsPerPage * 2) // Preload 2x more items
        .toList();

    // Use microtask for non-blocking preloading
    Future.microtask(() async {
      for (var item in nextChunk) {
        if (item is LessonModel) {
          for (var page in item.pages ?? []) {
            if (page.pagePhotoLink != null && page.pagePhotoLink!.isNotEmpty) {
              try {
                await lessonController.cacheManager
                    .getSingleFile(page.pagePhotoLink!);
              } catch (e) {
                debugPrint('Error preloading lesson image: $e');
              }
            }
          }
        } else if (item is QuizModel) {
          if (item.quizImageLink != null && item.quizImageLink!.isNotEmpty) {
            try {
              await lessonController.cacheManager
                  .getSingleFile(item.quizImageLink!);
            } catch (e) {
              debugPrint('Error preloading quiz image: $e');
            }
          }
        } else if (item is ShuffleQuizModel) {
          for (var question in item.questionsList ?? []) {
            if (question.qsImage != null && question.qsImage!.isNotEmpty) {
              try {
                await lessonController.cacheManager
                    .getSingleFile(question.qsImage!);
              } catch (e) {
                debugPrint('Error preloading shuffle quiz image: $e');
              }
            }
          }
        }
      }
    });
  }

  // Load more items with debouncing
  void _loadMoreItems() async {
    if (_isLoadingMore) return;
    setState(() => _isLoadingMore = true);

    if (widget.isForLibrary) {
      lessonController.loadMoreLibraryDisplayItems();
    } else {
      lessonController.loadMoreHomeDisplayItems();
    }

    // Preload ads for next indices
    final currentLength = widget.isForLibrary
        ? lessonController.libraryDisplayedItems.length
        : lessonController.homeDisplayedItems.length;
    if (!(profileController.userr.value.isPremiumUser ?? false)) {
      final nextAdIndex = currentLength + 3;
      if (!_bannerAds.containsKey(nextAdIndex)) {
        _createBannerAd(nextAdIndex);
      }
    }

    // Preload images for next items
    _preloadNextItems();

    setState(() => _isLoadingMore = false);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Obx(() {
          final isPremiumUser =
              profileController.userr.value.isPremiumUser ?? false;
          final itemsToDisplay = widget.isForLibrary
              ? lessonController.libraryDisplayedItems
              : lessonController.homeDisplayedItems;

          if (itemsToDisplay.isEmpty) {
            return ListView(
              children: [
                interestsWidget(context, widget.isForLibrary),
                const SizedBox(height: 20),
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(50.0),
                    child: CircularProgressIndicator(
                      color: mainColor,
                    ),
                  ),
                ),
              ],
            );
          }

          return ListView.builder(
            controller: widget.scrollController,
            physics: const ClampingScrollPhysics(),
            itemCount: 1 +
                itemsToDisplay.length +
                (isPremiumUser ? 0 : _countAdsBefore(itemsToDisplay.length)),
            itemBuilder: (context, index) {
              if (index == 0) {
                return Column(
                  children: [
                    interestsWidget(context, widget.isForLibrary),
                    const SizedBox(height: 20), // Space after interests widget
                  ],
                );
              }

              final adjustedIndex = index - 1;
              if (!isPremiumUser && _isAdIndex(adjustedIndex)) {
                return Padding(
                  padding: const EdgeInsets.fromLTRB(
                      16, 10, 16, 20), // Adjusted for spacing
                  child: _buildAdWidget(adjustedIndex),
                );
              }

              final itemIndex =
                  _calculateItemIndex(adjustedIndex, isPremiumUser);
              if (itemIndex < 0 || itemIndex >= itemsToDisplay.length) {
                return const SizedBox.shrink();
              }

              final item = itemsToDisplay[itemIndex];
              final bool isLastItem = itemIndex == itemsToDisplay.length - 1;

              return Padding(
                padding: EdgeInsets.fromLTRB(16, 16, 16,
                    isLastItem ? 50 : 20), // Consistent 20px spacing
                child: _buildItemWidget(item),
              );
            },
          );
        }),
        if (_showScrollToTopButton)
          Positioned(
            bottom: 20,
            right: 20,
            child: GestureDetector(
              onTap: _scrollToTop,
              child: Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: mainColorsGradient,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(2, 4),
                    ),
                  ],
                ),
                child: const Icon(Icons.arrow_upward, color: Colors.white),
              ),
            ),
          ),
      ],
    );
  }

  bool _isAdIndex(int i) {
    return i >= 3 && i % 3 == 0;
  }

  void _createBannerAd(int index) {
    if (_bannerAds.containsKey(index)) return;

    final adUnitId = _adUnitMap[index] ?? _adUnitMap[-1]!;

    final BannerAd banner = BannerAd(
      adUnitId: adUnitId,
      size: AdSize(
        width: Get.width.toInt() - AppConstants.adWidthMargin.toInt(),
        height: AppConstants.adHeight.toInt(),
      ),
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          if (!mounted) {
            ad.dispose();
            return;
          }
          setState(() {
            _loadedAdIndices.add(index);
          });
          debugPrint('Ad loaded at index $index');
        },
        onAdFailedToLoad: (ad, error) {
          if (!mounted) {
            ad.dispose();
            return;
          }
          ad.dispose();
          setState(() {
            _bannerAds.remove(index);
            _loadedAdIndices.remove(index);
          });
          debugPrint('Ad failed to load at index $index: $error');
        },
      ),
    );

    _bannerAds[index] = banner;
    banner.load();
  }

  Widget _buildAdWidget(int adjustedIndex) {
    final isAdLoaded = _bannerAds.containsKey(adjustedIndex) &&
        _loadedAdIndices.contains(adjustedIndex);

    if (!isAdLoaded && !_bannerAds.containsKey(adjustedIndex)) {
      _createBannerAd(adjustedIndex);
    }

    return SizedBox(
      width: Get.width - AppConstants.adWidthMargin,
      height: AppConstants.adHeight + 35, // Increased to fix 3.0px overflow
      child: Column(
        children: [
          const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('REKLAMA', style: TextStyle(fontSize: 16)),
            ],
          ),
          const SizedBox(height: 8),
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: SizedBox(
              width: Get.width - AppConstants.adWidthMargin,
              height: AppConstants.adHeight,
              child: isAdLoaded
                  ? AdWidget(ad: _bannerAds[adjustedIndex]!)
                  : Container(
                      width: Get.width - AppConstants.adWidthMargin,
                      height: AppConstants.adHeight,
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Center(child: CircularProgressIndicator()),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  int _countAdsBefore(int length) {
    return _loadedAdIndices.where((adIndex) => adIndex < length).length;
  }

  int _calculateItemIndex(int listIndex, bool isPremiumUser) {
    if (isPremiumUser) return listIndex;
    final adsBefore =
        _loadedAdIndices.where((adIndex) => adIndex <= listIndex).length;
    return listIndex - adsBefore;
  }

  Widget _buildItemWidget(dynamic item) {
    if (item is LessonModel) {
      return BoxWidget(lesson: item);
    } else if (item is QuizModel) {
      return BoxWidget(quiz: item);
    } else if (item is ShuffleQuizModel) {
      return BoxWidget(shuffleQuiz: item);
    }
    return const SizedBox.shrink();
  }
}

class AppConstants {
  static const double adHeight = 300;
  static const double adWidthMargin = 50;
  static const double minScrollForButton = 300;
  static const double infiniteScrollOffset = 200;
}
