import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../controllers/profile_controller.dart';
import '../models/league_user_model.dart';

/// Cache class for league standings
class _CachedStandings {
  final List<LeaguePlayerModel> standings;
  final DateTime timestamp;

  _CachedStandings(this.standings, this.timestamp);

  bool get isExpired =>
      DateTime.now().difference(timestamp) > const Duration(minutes: 5);
}

/// Professional leaderboard service that handles all league-related operations
class LeaderboardService {
  static const List<String> _leagues = [
    'Bronzana',
    'Srebrna',
    '<PERSON><PERSON>na',
    '<PERSON>',
    '<PERSON>jamantska',
    '<PERSON>',
    '<PERSON><PERSON><PERSON>',
    'Smaragdna',
    'Topaz',
    'Ametist',
    'Opal',
    'Kvarc',
    'Žad',
    '<PERSON><PERSON><PERSON>',
    'Fenix',
    'Oniks',
    'Galaktička',
    'Koralska',
    'Jupiter',
    'Elitna'
  ];

  static const int _maxPlayersPerGroup = 10;
  static const String _defaultLeague = 'Bronzana';

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _userId;

  // Cache for league standings to improve performance
  static final Map<String, _CachedStandings> _standingsCache = {};

  LeaderboardService(this._userId);

  /// Adds a user to the appropriate league group
  Future<void> addUserToLeague({
    required String username,
    String? targetLeague,
  }) async {
    try {
      final league = targetLeague ?? _defaultLeague;

      if (!_leagues.contains(league)) {
        throw ArgumentError('Invalid league: $league');
      }

      final groupId = await _findAvailableGroup(league);
      await _addUserToGroup(league, groupId, username);

      // Update user's league and group information
      await _updateUserLeagueInfo(league, groupId);
    } catch (e) {
      _logError('Failed to add user to league', e);
      rethrow;
    }
  }

  /// Updates the user's weekly score and leaderboard position
  Future<void> updateWeeklyScore(int scoreToAdd) async {
    try {
      final profileController = Get.find<ProfileController>();
      final league = profileController.userr.value.league ?? _defaultLeague;
      final groupId = profileController.userr.value.groupId;

      if (groupId == null || groupId.isEmpty) {
        _logWarning('User not assigned to any group, adding to league');
        await addUserToLeague(
          username: profileController.userr.value.uniqueName ?? 'Unknown',
          targetLeague: league,
        );
        return;
      }

      await Future.wait([
        _updateUserWeeklyScore(scoreToAdd),
        _updateLeaderboardScore(scoreToAdd, league, groupId),
      ]);
    } catch (e) {
      _logError('Failed to update weekly score', e);
      rethrow;
    }
  }

  /// Finds an available group in the specified league
  Future<String> _findAvailableGroup(String league) async {
    try {
      final groupsSnapshot = await _firestore
          .collection('leaderboards')
          .doc(league)
          .collection('groups')
          .get();

      // Check existing groups for available space
      for (final groupDoc in groupsSnapshot.docs) {
        final playersSnapshot =
            await groupDoc.reference.collection('players').get();

        if (playersSnapshot.docs.length < _maxPlayersPerGroup) {
          return groupDoc.id;
        }
      }

      // Create new group if no space available
      final newGroupId = 'group_${groupsSnapshot.docs.length + 1}';
      return newGroupId;
    } catch (e) {
      _logError('Failed to find available group', e);
      rethrow;
    }
  }

  /// Adds user to a specific group
  Future<void> _addUserToGroup(
      String league, String groupId, String username) async {
    try {
      await _firestore.runTransaction((transaction) async {
        final groupRef = _firestore
            .collection('leaderboards')
            .doc(league)
            .collection('groups')
            .doc(groupId);

        final playerRef = groupRef.collection('players').doc(_userId);

        // Create group if it doesn't exist
        final groupDoc = await transaction.get(groupRef);
        if (!groupDoc.exists) {
          transaction.set(groupRef, {
            'createdAt': Timestamp.now(),
            'league': league,
            'groupId': groupId,
          });
        }

        // Add player to group
        final playerData = LeaguePlayerModel(
          username: username,
          league: league,
          score: 0,
          playerId: _userId,
          lastUpdated: Timestamp.now(),
        );

        transaction.set(playerRef, playerData.toJson());
      });
    } catch (e) {
      _logError('Failed to add user to group', e);
      rethrow;
    }
  }

  /// Updates user's league and group information in their profile
  Future<void> _updateUserLeagueInfo(String league, String groupId) async {
    try {
      final profileController = Get.find<ProfileController>();

      await _firestore.collection('users').doc(_userId).update({
        'league': league,
        'groupId': groupId,
        'lastUpdated': Timestamp.now(),
      });

      // Update local state
      profileController.userr.value.league = league;
      profileController.userr.value.groupId = groupId;
      profileController.userr.refresh();
    } catch (e) {
      _logError('Failed to update user league info', e);
      rethrow;
    }
  }

  /// Updates user's weekly score in their profile
  Future<void> _updateUserWeeklyScore(int scoreToAdd) async {
    try {
      final profileController = Get.find<ProfileController>();

      await _firestore.collection('users').doc(_userId).update({
        'weeklyScore': FieldValue.increment(scoreToAdd),
      });

      // Update local state
      profileController.userr.value.weeklyScore =
          (profileController.userr.value.weeklyScore ?? 0) + scoreToAdd;
      profileController.userr.refresh();
    } catch (e) {
      _logError('Failed to update user weekly score', e);
      rethrow;
    }
  }

  /// Updates leaderboard score for the user
  Future<void> _updateLeaderboardScore(
      int scoreToAdd, String league, String groupId) async {
    try {
      final profileController = Get.find<ProfileController>();
      final playerRef = _firestore
          .collection('leaderboards')
          .doc(league)
          .collection('groups')
          .doc(groupId)
          .collection('players')
          .doc(_userId);

      final playerDoc = await playerRef.get();

      if (!playerDoc.exists) {
        // Create new player document
        final username = profileController.userr.value.uniqueName ?? 'Unknown';
        final newPlayer = LeaguePlayerModel(
          username: username,
          league: league,
          score: scoreToAdd,
          playerId: _userId,
          lastUpdated: Timestamp.now(),
        );

        await playerRef.set(newPlayer.toJson());
      } else {
        // Update existing player score
        final currentScore = playerDoc.data()?['score'] ?? 0;
        await playerRef.update({
          'score': currentScore + scoreToAdd,
          'lastUpdated': Timestamp.now(),
        });
      }
    } catch (e) {
      _logError('Failed to update leaderboard score', e);
      rethrow;
    }
  }

  /// Gets the current league standings for the user's group with caching
  Future<List<LeaguePlayerModel>> getLeagueStandings() async {
    try {
      final profileController = Get.find<ProfileController>();
      final league = profileController.userr.value.league ?? _defaultLeague;
      final groupId = profileController.userr.value.groupId;

      if (groupId == null || groupId.isEmpty) {
        return [];
      }

      final cacheKey = '${league}_$groupId';

      // Check cache first
      if (_standingsCache.containsKey(cacheKey)) {
        final cached = _standingsCache[cacheKey]!;
        if (!cached.isExpired) {
          return cached.standings;
        } else {
          _standingsCache.remove(cacheKey);
        }
      }

      final playersSnapshot = await _firestore
          .collection('leaderboards')
          .doc(league)
          .collection('groups')
          .doc(groupId)
          .collection('players')
          .orderBy('score', descending: true)
          .orderBy('lastUpdated', descending: false)
          .get();

      final standings = playersSnapshot.docs
          .map((doc) => LeaguePlayerModel.fromSnap(doc))
          .toList();

      // Cache the results
      _standingsCache[cacheKey] = _CachedStandings(standings, DateTime.now());

      return standings;
    } catch (e) {
      _logError('Failed to get league standings', e);
      return [];
    }
  }

  /// Clears the standings cache
  static void clearCache() {
    _standingsCache.clear();
  }

  /// Utility methods for logging
  void _logError(String message, dynamic error) {
    print('LeaderboardService Error: $message - $error');
  }

  void _logWarning(String message) {
    print('LeaderboardService Warning: $message');
  }

  /// Static helper methods
  static List<String> get leagues => List.unmodifiable(_leagues);
  static String get defaultLeague => _defaultLeague;
  static int get maxPlayersPerGroup => _maxPlayersPerGroup;

  /// Gets league index (0-based)
  static int getLeagueIndex(String league) {
    return _leagues.indexOf(league);
  }

  /// Gets next league in hierarchy
  static String? getNextLeague(String currentLeague) {
    final index = getLeagueIndex(currentLeague);
    if (index == -1 || index >= _leagues.length - 1) return null;
    return _leagues[index + 1];
  }

  /// Gets previous league in hierarchy
  static String? getPreviousLeague(String currentLeague) {
    final index = getLeagueIndex(currentLeague);
    if (index <= 0) return null;
    return _leagues[index - 1];
  }
}
