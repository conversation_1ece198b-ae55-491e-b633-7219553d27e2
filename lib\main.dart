import 'package:bibl/controllers/auth_controller.dart';
import 'package:bibl/controllers/heart_controller.dart' show HeartController;
import 'package:bibl/controllers/leaderboard_controller.dart';
import 'package:bibl/services/ad_mob_service.dart';
import 'package:bibl/splash.dart';
import 'package:figma_to_flutter/figma_to_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'controllers/audio_controller.dart';
import 'controllers/category_controller.dart';
import 'controllers/lesson_controller.dart';
import 'controllers/quiz_controller.dart';
import 'res/style.dart';
import 'services/notification_service.dart';
import 'package:timezone/data/latest_all.dart' as tz;

import 'services/shared_preferences.dart';

// Define a background message handler for handling notifications when the app is in the background or terminated
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  // print("Handling a background message: ${message.messageId}");
}

// Aggressive image preloading for super-fast display
void _startAggressiveImagePreloading() {
  // Start preloading in background without blocking app startup
  Future.microtask(() async {
    try {
      final lessonController = Get.find<LessonController>();
      final quizController = Get.find<QuizController>();

      // Start preloading immediately with cached data
      await lessonController.startAggressiveImagePreloading();
      await quizController.startAggressiveImagePreloading();
    } catch (e) {
      debugPrint('Error in aggressive image preloading: $e');
    }
  });
}

// void getToken() async {
//   FirebaseMessaging messaging = FirebaseMessaging.instance;

//   try {
//     String? token = await messaging.getToken();
//     if (token != null) {
//       print("FCM Token: $token");
//       // You can use the token here, for example, show it in a toast
//     } else {
//       print("Failed to fetch FCM Token");
//     }
//   } catch (e) {
//     print("Error fetching FCM Token: $e");
//   }
// }

Future<void> main() async {
  await dotenv.load(fileName: ".env");
  WidgetsFlutterBinding.ensureInitialized();
  await SharedPrefs.sharedPreferencesInitialization();
  // // Set up test device ID
  // final RequestConfiguration requestConfiguration = RequestConfiguration(
  //   testDeviceIds: [
  //     "B827B498FD1EA3F5FE7AC6D54C82251B"
  //   ], // Add your test device ID here
  // );

  // MobileAds.instance.updateRequestConfiguration(requestConfiguration);

  MobileAds.instance.initialize();
  await Firebase.initializeApp();
  Get.put(HeartController(), permanent: true);
  Get.put(CategoryController(), permanent: true);
  Get.put(AudioController(), permanent: true);
  Get.put(AuthController(), permanent: true);
  Get.put(QuizController(), permanent: true);
  Get.put(LessonController(), permanent: true);
  Get.put(RewardedAdManagerController(), permanent: true);
  Get.put(LeaderboardController(), permanent: true);

  // Fetch all categories before the app starts
  await Get.find<CategoryController>().fetchAllCategories();

  // Start aggressive image preloading immediately
  _startAggressiveImagePreloading();
  // Initialize Firebase Messaging

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // Set up foreground notification display
  FirebaseMessaging.onMessage.listen((RemoteMessage message) {
    if (message.notification != null) {
      // Use NotificationService to display notifications in the foreground if needed
      NotificationService().showNotification(
        title: message.notification!.title ?? '',
        body: message.notification!.body ?? '',
      );
    }
  });
  // getToken();
  NotificationService().initNotification();
  tz.initializeTimeZones();

  // Initialize other dependencies
  Figma.setup(deviceWidth: 375, deviceHeight: 812);

  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarBrightness: Brightness.dark,
    statusBarIconBrightness: Brightness.dark,
  ));

  // runApp(DevicePreview(enabled: !kReleaseMode, builder: (context) => MyApp()));
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      // useInheritedMediaQuery: true,
      // locale: DevicePreview.locale(context),
      // builder: DevicePreview.appBuilder,
      defaultTransition: Transition.fade,
      transitionDuration: const Duration(milliseconds: 300),
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: myCustomPrimarySwatch,
      ),
      home: const Splash(),
    );
  }
}
